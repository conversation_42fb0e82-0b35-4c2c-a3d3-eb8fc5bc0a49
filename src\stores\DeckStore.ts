import { types, Instance, getRoot, flow } from "mobx-state-tree";
import { TrackInfoModel, LibraryStoreInstance } from "./LibraryStore"; // Import TrackInfoModel and LibraryStoreInstance
import { RootStoreType } from "./RootStore"; // Import RootStoreType for getRoot
import { db } from "../services/DatabaseService"; // Import db for persistence
import { Segment } from '../../lib/wavesurfer.js/src/plugins/segments';
import { DeckAudioEngine } from "../services/AudioEngine"; // Import the new audio engine


export const DeckStoreModel = types
  .model("DeckStore", {
    id: types.identifier,
    loadedTrack: types.maybeNull(types.reference(TrackInfoModel)), // Reference to a TrackInfoModel
    isPlaying: types.optional(types.boolean, false),
    currentBpm: types.optional(types.number, 0),
    effectiveDuration: types.optional(types.number, 0),
    originalBpm: types.optional(types.number, 0),
    currentTime: types.optional(types.number, 0),
    currentBeat: types.optional(types.number, 0),
    currentBar: types.optional(types.number, 0),
    currentPhrase: types.optional(types.number, 0),
    playbackRate: types.optional(types.number, 1.0),
    timeOffset: types.optional(types.number, 0),
    segments: types.optional(types.array(types.frozen<Segment>()), []), // Array of segments
    // EQ settings
    lowEQ: types.optional(types.number, 0),
    midEQ: types.optional(types.number, 0), // Used in 3-band mode
    midLoEQ: types.optional(types.number, 0), // Used in 4-band mode
    midHiEQ: types.optional(types.number, 0), // Used in 4-band mode
    highEQ: types.optional(types.number, 0),
    // Filter setting
    filterValue: types.optional(types.number, 50), // Default filter value (middle position)
    volume: types.optional(types.number, 0.7), // Default volume (same as in AudioEngine)
    masterTempo: types.optional(types.boolean, false), // Master Tempo (time-shifting without pitch change)
    // Master Deck and Sync settings
    isMaster: types.optional(types.boolean, false), // Whether this deck is the master deck
    syncEnabled: types.optional(types.boolean, false), // Whether sync is enabled for this deck
    nudgeValue: types.optional(types.number, 0), // Current nudge value (-1 to 1)
  })
  .volatile<{ audioEngine: DeckAudioEngine | null, stateLoaded: boolean }>(() => ({
    audioEngine: null, // Will be initialized in afterCreate
    stateLoaded: false,
  }))
  .actions((self) => {
    // Initialize the audio engine
    const initAudioEngine = flow(function* () {
      if (!self.audioEngine) {
        const rootStore = getRoot<RootStoreType>(self);
        if (!rootStore) {
          console.error(`Deck ${self.id}: Failed to get root store when initializing audio engine`);
          return;
        }

        // Check if audio services are initialized
        if (!rootStore.audioServicesInitialized) {
          console.log(`Deck ${self.id}: Audio services not yet initialized, skipping audio engine creation`);
          return;
        }

        try {
          self.audioEngine = new DeckAudioEngine(rootStore, self as DeckStoreInstance);
          console.log(`Deck ${self.id} audio engine initialized with rootStore:`, !!rootStore);

          // if we have a track for the deck load it
          if (self.loadedTrack) {
            yield loadTrack(self.loadedTrack.id);
          }

        } catch (error) {
          console.error(`Deck ${self.id}: Failed to initialize audio engine:`, error);
        }
      }
    });

    // Clean up the audio engine
    const cleanupAudioEngine = () => {
      if (self.audioEngine) {
        self.audioEngine.dispose();
        self.audioEngine = null;
        console.log(`Deck ${self.id} audio engine disposed`);
      }
    };

    const loadTrack = flow(function* loadTrack(trackId: string) {
      const rootStore = getRoot<RootStoreType>(self);
      const libraryStore: LibraryStoreInstance = rootStore.libraryStore;

      const track = libraryStore.tracks.get(trackId);

      if (track) {
        try {
          // Ensure we have an audio engine
          initAudioEngine();

          // If we were playing, stop first
          if (self.isPlaying) {
            pause();
          }

          self.loadedTrack = track;
          // Ensure analysis data is loaded before accessing BPM
          if (!track.hasAnalysisData) {
            yield track.loadAnalysisData(); // Wait for analysis data to load
          }

          // Set track properties
          self.originalBpm = track.bpm;
          self.currentBpm = track.bpm;
          self.effectiveDuration = track.duration;
          self.timeOffset = track.timeOffset;
          self.isPlaying = false; // Reset playback state
          self.currentTime = 0; // Reset time
          self.currentBeat = 0; // Reset beat
          self.currentBar = 0; // Reset bar
          self.currentPhrase = 0; // Reset phrase
          self.playbackRate = 1.0; // Reset playback rate
          self.segments = track.segments; // Set segments from track

          // Load the audio buffer in the audio engine
          yield self.audioEngine!.loadTrack(trackId);
          console.log(`Track ${track.filename} loaded onto deck ${self.id} with audio buffer`);

          if(self.effectiveDuration === 0)
            self.effectiveDuration = self.audioEngine!.getDuration();
          // Save deck state after loading a track
          saveDeckState();
        } catch (error) {
          console.error(`Error loading audio for track ${trackId}:`, error);
          // Handle error - maybe show a notification to the user
        }
      } else {
        console.error(`Track with ID ${trackId} not found in library.`);
        self.loadedTrack = null;
        self.originalBpm = 0;
        self.currentBpm = 0;
        self.effectiveDuration = 0;
        self.timeOffset = 0;
        self.isPlaying = false;
      }
    });

    const play = flow(function* play() {
      if (self.loadedTrack && self.audioEngine) {
        // Check if audio engine has loaded the buffer before playing
        if (!self.audioEngine.isBufferLoaded()) {
          console.log(`Deck ${self.id}: Audio buffer not loaded yet, loading track first...`);
          // Try to load the track again before playing
          try {
            yield loadTrack(self.loadedTrack.id);
            // Now try to play again after loading
            if (self.audioEngine?.isBufferLoaded()) {
              self.audioEngine.play();
              self.isPlaying = true;
              console.log(`Deck ${self.id} playing track ${self.loadedTrack?.filename} after loading buffer`);
            } else {
              console.warn(`Deck ${self.id}: Buffer still not loaded after reload attempt`);
            }
          } catch (error) {
            console.error(`Deck ${self.id}: Error reloading track:`, error);
          }
          return;
        }

        // Start playback using the audio engine
        self.audioEngine.play();
        self.isPlaying = true;
        console.log(`Deck ${self.id} playing track ${self.loadedTrack.filename}`);
      } else {
        console.warn(`Deck ${self.id}: No track loaded to play or audio engine not initialized.`);
      }
    });

    const pause = () => {
      if (self.audioEngine) {
        // Pause playback using the audio engine
        self.audioEngine.pause();
      }
      self.isPlaying = false;
      console.log(`Deck ${self.id} paused`);
      // Save deck state when pausing
      saveDeckState();
    };

    const seek = (timeInSeconds: number) => {
      if (self.loadedTrack && self.audioEngine) {
        // Seek using the audio engine
        self.audioEngine.seek(timeInSeconds);
        console.log(`Deck ${self.id} seeking to ${timeInSeconds}s`);
        // The audio engine will update self.currentTime
        // Save deck state after seeking
        saveDeckState();
      } else if (self.loadedTrack) {
        // If we don't have an audio engine yet, just update the time
        self.currentTime = timeInSeconds;
        console.log(`Deck ${self.id} seeking to ${timeInSeconds}s (no audio engine)`);
        saveDeckState();
      }
    };

    const setPlaybackRate = (rate: number) => {
        self.playbackRate = rate;
        // Update the audio engine if it exists
        if (self.audioEngine) {
          // Pass the current playback rate and master tempo setting
          self.audioEngine.setPlaybackRate(rate);
        }
        // Update BPM based on playback rate
        if (self.originalBpm > 0) {
             self.currentBpm = self.originalBpm * rate;
        }

        // If we have a loaded track, update any components that need to know about the effective duration
        if (self.effectiveDuration > 0) {
          // The effective duration changes inversely with playback rate
          self.effectiveDuration = self.effectiveDuration = self.audioEngine ? self.audioEngine.getDuration() / self.playbackRate : 0;
        }

        console.log(`Deck ${self.id} playback rate set to ${rate}, master tempo: ${self.masterTempo}`);
        // Save deck state after changing playback rate
        saveDeckState();
    };

    // New actions for EQ and volume control
    const setEQ = (low: number, mid: number, high: number, midLo?: number, midHi?: number) => {
      self.lowEQ = low;
      self.highEQ = high;

      const rootStore = getRoot<RootStoreType>(self);
      const is4BandMode = rootStore.settingsStore.eqBands === "4-band";

      if (is4BandMode) {
        // 4-band mode
        if (midLo !== undefined) self.midLoEQ = midLo;
        if (midHi !== undefined) self.midHiEQ = midHi;

        if (self.audioEngine) {
          self.audioEngine.setEQ(low, mid, high, midLo, midHi);
        }

        console.log(`Deck ${self.id} EQ set to low: ${low}, mid-lo: ${midLo}, mid-hi: ${midHi}, high: ${high}`);
      } else {
        // 3-band mode
        self.midEQ = mid;

        if (self.audioEngine) {
          self.audioEngine.setEQ(low, mid, high);
        }

        console.log(`Deck ${self.id} EQ set to low: ${low}, mid: ${mid}, high: ${high}`);
      }
      saveDeckState();
    };

    const setVolume = (volume: number) => {
      self.volume = volume;

      if (self.audioEngine) {
        self.audioEngine.setVolume(volume);
      }

      console.log(`Deck ${self.id} volume set to ${volume}`);

      saveDeckState();
    };

    const setMasterTempo = (enabled: boolean) => {
      self.masterTempo = enabled;

      // Update the audio engine's master tempo state
      if (self.audioEngine) {
        console.log(`Deck ${self.id} updating audio engine master tempo to ${enabled}`);
        self.audioEngine.setMasterTempo(enabled);
      }

      console.log(`Deck ${self.id} master tempo ${enabled ? 'enabled' : 'disabled'}`);

      // Save deck state after changing master tempo
      saveDeckState();
    };

    // New action for filter control
    const setFilterValue = (value: number) => {
      self.filterValue = value;

      if (self.audioEngine) {
        // Apply filter to audio engine
        // This will depend on your audio engine implementation
        // self.audioEngine.setFilter(value);
        console.log(`Deck ${self.id} filter set to ${value}`);
      }

      // Save deck state after changing filter
      saveDeckState();
    };

    // Master Deck and Sync actions
    const setIsMaster = (isMaster: boolean) => {
      self.isMaster = isMaster;
      console.log(`Deck ${self.id} master status set to ${isMaster}`);
      saveDeckState();
    };

    const setSyncEnabled = (enabled: boolean) => {
      self.syncEnabled = enabled;
      console.log(`Deck ${self.id} sync ${enabled ? 'enabled' : 'disabled'}`);
      saveDeckState();
    };

    const setNudgeValue = (value: number) => {
      // Clamp nudge value between -1 and 1
      self.nudgeValue = Math.max(-1, Math.min(1, value));

      // Apply nudge to playback rate if audio engine exists
      if (self.audioEngine && self.loadedTrack) {
        // Nudge affects playback rate temporarily
        const nudgeRate = self.playbackRate + (self.nudgeValue * 0.1); // 10% max nudge
        self.audioEngine.setPlaybackRate(nudgeRate);
      }
    };

    const resetNudge = () => {
      self.nudgeValue = 0;

      // Reset playback rate to original value
      if (self.audioEngine && self.loadedTrack) {
        self.audioEngine.setPlaybackRate(self.playbackRate);
      }
    };

    // Actions to be called externally based on audio events
    const setCurrentBeat = (beat: number) => { self.currentBeat = beat; };
    const setCurrentBar = (bar: number) => { self.currentBar = bar; };
    const setCurrentPhrase = (phrase: number) => { self.currentPhrase = phrase; };

    // Create a debounced version of saveDeckState
    const createDebouncedSaveDeckState = () => {
      let timeoutId: NodeJS.Timeout | null = null;

      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        timeoutId = setTimeout(() => {
          // Only save if we have a loaded track
          if (self.loadedTrack) {
            const deckState = {
              loadedTrackId: self.loadedTrack.id,
              currentTime: self.currentTime,
              playbackRate: self.playbackRate,
              timeOffset: self.timeOffset,
              masterTempo: self.masterTempo,
              // Save EQ gain values
              lowEQ: self.lowEQ,
              midEQ: self.midEQ,
              midLoEQ: self.midLoEQ,
              midHiEQ: self.midHiEQ,
              highEQ: self.highEQ,
              // Save filter value
              filterValue: self.filterValue,
              // Save volume
              volume: self.volume,
              // Save sync settings
              isMaster: self.isMaster,
              syncEnabled: self.syncEnabled,
            };

            db.saveSetting({
              key: `deckState_${self.id}`,
              value: deckState
            }).catch(error => {
              console.error(`Failed to save deck ${self.id} state:`, error);
            });
          }
          timeoutId = null;
        }, 5000);
      };
    };

    // Create the debounced function
    const saveDeckStateDebounced = createDebouncedSaveDeckState();

    const setCurrentTime = (time: number) => {
      self.currentTime = time;

      // Use the debounced function instead of the throttled one
      saveDeckStateDebounced();
    };

    // Save deck state to database
    const saveDeckState = flow(function* () {
      try {
        // Only save if we have a loaded track
        if (self.loadedTrack) {
          const deckState = {
            loadedTrackId: self.loadedTrack.id,
            currentTime: self.currentTime,
            playbackRate: self.playbackRate,
            timeOffset: self.timeOffset,
            masterTempo: self.masterTempo,
            // Save EQ gain values
            lowEQ: self.lowEQ,
            midEQ: self.midEQ,
            midLoEQ: self.midLoEQ,
            midHiEQ: self.midHiEQ,
            highEQ: self.highEQ,
            // Save filter value
            filterValue: self.filterValue,
            // Save volume
            volume: self.volume,
            // Save sync settings
            isMaster: self.isMaster,
            syncEnabled: self.syncEnabled,
            // We don't save isPlaying as we want decks to start paused when restored
          };

          yield db.saveSetting({
            key: `deckState_${self.id}`,
            value: deckState
          });

          console.log(`Deck ${self.id} state saved successfully`);
        }
      } catch (error) {
        console.error(`Failed to save deck ${self.id} state:`, error);
      }
    });

    // Throttled version of saveDeckState to avoid too frequent saves
    // let saveTimeout: NodeJS.Timeout | null = null;
    // const saveDeckStateThrottled = () => {
    //   if (saveTimeout) {
    //     clearTimeout(saveTimeout);
    //   }

    //   saveTimeout = setTimeout(() => {
    //     // Call the flow function through the actions object to maintain context
    //     actions.saveDeckState();
    //     saveTimeout = null;
    //   }, 5000); // Save at most every 5 seconds
    // };

    // Load deck state from database
    const loadDeckState = flow(function* () {
      try {
        const deckStateSetting = yield db.getSetting(`deckState_${self.id}`);

        if (deckStateSetting && deckStateSetting.value) {
          const deckState = deckStateSetting.value;

          // Load the track if we have a track ID
          if (deckState.loadedTrackId) {
            yield loadTrack(deckState.loadedTrackId);

            // Set the playback position and rate
            if (deckState.currentTime !== undefined) {
              self.currentTime = deckState.currentTime;
            }

            if (deckState.playbackRate !== undefined) {
              self.playbackRate = deckState.playbackRate;
              // Update BPM if we have original BPM
              if (self.originalBpm > 0) {
                self.currentBpm = self.originalBpm * self.playbackRate;
              }
              // Update effective duration
              if (self.effectiveDuration > 0) {
                self.effectiveDuration = self.audioEngine ? self.audioEngine.getDuration() / self.playbackRate : 0;
              }
            }

            if (deckState.timeOffset !== undefined) {
              self.timeOffset = deckState.timeOffset;
            }

            if (deckState.masterTempo !== undefined) {
              self.masterTempo = deckState.masterTempo;
            }

            // Restore EQ gain values if they exist
            if (deckState.lowEQ !== undefined) {
              self.lowEQ = deckState.lowEQ;
            }
            if (deckState.midEQ !== undefined) {
              self.midEQ = deckState.midEQ;
            }
            if (deckState.midLoEQ !== undefined) {
              self.midLoEQ = deckState.midLoEQ;
            }
            if (deckState.midHiEQ !== undefined) {
              self.midHiEQ = deckState.midHiEQ;
            }
            if (deckState.highEQ !== undefined) {
              self.highEQ = deckState.highEQ;
            }

            // Restore filter value if it exists
            if (deckState.filterValue !== undefined) {
              self.filterValue = deckState.filterValue;
            }

            // Restore volume if it exists
            if (deckState.volume !== undefined) {
              self.volume = deckState.volume;
            }

            // Restore sync settings if they exist
            if (deckState.isMaster !== undefined) {
              self.isMaster = deckState.isMaster;
            }
            if (deckState.syncEnabled !== undefined) {
              self.syncEnabled = deckState.syncEnabled;
            }

            // Apply the EQ settings to the audio engine
            if (self.audioEngine) {
              const rootStore = getRoot<RootStoreType>(self);
              const is4BandMode = rootStore.settingsStore.eqBands === "4-band";

              if (is4BandMode) {
                self.audioEngine.setEQ(self.lowEQ, 0, self.highEQ, self.midLoEQ, self.midHiEQ);
              } else {
                self.audioEngine.setEQ(self.lowEQ, self.midEQ, self.highEQ);
              }

              // Apply volume
              self.audioEngine.setVolume(self.volume);
            }

            // Always start paused
            self.isPlaying = false;
            self.stateLoaded = true;

            console.log(`Deck ${self.id} state loaded successfully`);
          }
        }
      } catch (error) {
        console.error(`Failed to load deck ${self.id} state:`, error);
      }
    });

    // Set up periodic state saving for time updates
    const startPeriodicSaving = () => {
      // Save state every 30 seconds if track is loaded and playing
      const intervalId = setInterval(() => {
        if (self.loadedTrack && self.isPlaying) {
          saveDeckState();
        }
      }, 30000); // 30 seconds

      return () => clearInterval(intervalId); // Return cleanup function
    };

    // Initialize periodic saving
    let cleanupPeriodicSaving: (() => void) | null = null;

    // Lifecycle hooks
    const afterCreate = () => {
      // Start periodic saving
      cleanupPeriodicSaving = startPeriodicSaving();
    };

    const afterAttach = () => {
      // Initialize the audio engine after the deck is attached to the root store
      initAudioEngine();
      console.log(`Deck ${self.id} afterAttach: Audio engine initialized`);
    };

    const beforeDestroy = () => {
      // Clean up the audio engine
      cleanupAudioEngine();

      // Clean up periodic saving
      if (cleanupPeriodicSaving) {
        cleanupPeriodicSaving();
      }

      // if (saveTimeout) {
      //   clearTimeout(saveTimeout);
      // }
    };

    return {
      loadTrack,
      play,
      pause,
      seek,
      setPlaybackRate,
      setEQ,
      setVolume,
      setMasterTempo,
      setFilterValue,
      setCurrentBeat,
      setCurrentBar,
      setCurrentPhrase,
      setCurrentTime,
      saveDeckState,
      loadDeckState,
      // Master Deck and Sync actions
      setIsMaster,
      setSyncEnabled,
      setNudgeValue,
      resetNudge,
      // Audio engine management
      initAudioEngine,
      cleanupAudioEngine,
      afterCreate,
      afterAttach,
      beforeDestroy,
    };
  });

export interface DeckStoreInstance extends Instance<typeof DeckStoreModel> {}
